<?php
if (version_compare(PHP_VERSION, '7.0.0', '<')) {
    die('当前服务器环境php版本小于7.0, 请使用7.0~8.0的版本以保障稳定性');
}
/**
 * 神秘人自助下单系统
 * 系统后台首页
 **/
$noloadFile = true;
include "../includes/common.php";
if ($isLogin != 1) {
    exit("<script language='javascript'>window.location.href='./login.php';</script>");
}
$title = '后台首页';

$mysqlversion = $DB->count("select VERSION()");

if (!function_exists('checkSec')) {
    exit('系统文件缺失或被损坏，请检测运行环境是否正常，如正常可下载更新包覆盖或重新安装');
}
$sec_msg = checkSec();

$style = '';
if ($is_mb == true) {
    $style = 'padding:10px 0 0 0;margin:0;';
} else {
    $style = 'padding-top:10px;';
}

function unicodeEncode($strLong)
{
    $strArr     = preg_split('/(?<!^)(?!$)/u', $strLong); //拆分字符串为数组(含中文字符)
    $resUnicode = '';
    foreach ($strArr as $str) {
        $bin_str = '';
        $arr     = is_array($str) ? $str : str_split($str); //获取字符内部数组表示
        foreach ($arr as $value) {
            $bin_str .= decbin(ord($value)); //转成数字再转成二进制字符串
        }
        $bin_str = preg_replace('/^.{4}(.{4}).{2}(.{6}).{2}(.{6})$/', '$1$2$3', $bin_str); //正则截取
        $unicode = dechex(bindec($bin_str)); //返回unicode十六进制
        $_sup    = '';
        for ($i = 0; $i < 4 - strlen($unicode); $i++) {
            $_sup .= '0'; //补位高字节 0
        }
        $str = '\\u' . $_sup . $unicode; //加上 \u 返回
        $resUnicode .= $str;
    }
    return $resUnicode;
}

//官方公告
$mysqlInfo = $DB->query("select VERSION()")->fetch();
include './head.php';
?>
<style href="../assets/public/animate.css/3.5.2/animate.min.css"></style>
<style type="text/css">
.GfArticle {
    display: flex;
    border-bottom: 1px solid #eee;
    border-top: 1px solid #eee;
    background: #fff;
    padding: .7rem;
    margin-bottom: 10px;
}
.GfArticle .title {
    display: inline-block;
    line-height: 25px;
    height: 25px;
}
.GfArticle .content {
    flex: 1;
    display: flex;
    font-size: 1.3rem;
    line-height: 25px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding-left: .1rem;
}
.GfArticle .content .article-content {
    flex: 1;
    line-height: 25px;
    height: 155px;
    min-height: 155px;
}
.GfArticle .content .article-content:hover {
    min-height: 460px;
}
.GfArticle .content .slide {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-left: 5px;
}
.GfArticle .content ul {
    padding: 2px 2px;
}
.GfArticle .content li {
    text-align: left;
    padding: 5px 2px;
    font-size: 1.25rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.GfArticle .content li {
    list-style-type: none;
}
.GfArticle .content li:hover,
.GfArticle .content li:active,
.GfArticle .content li:focus {
    list-style-type: none;
    margin-left: 3px;
}
.GfArticle .content .more {
    flex: 0.5;
}
.GfArticle .content .more a {
    padding: 3px 6px;
    background-color: #2196F3;
    color: #fff;
    border-radius: 0px;
}
.notice .content {
    padding: 5px;
}
.notice .notice-title {
    padding: 12px 2px;
    margin: auto;
    text-align: center;
}
.notice .notice-title h3 {
    font-size: 1.85rem;
    margin: 6px 5px;
}
.notice .notice-content img {
    max-width: 100%;
}
/* 优化布局，减少空白 */
.widget-content-full {
    padding: 0; /* 移除多余内边距 */
}
.row {
    margin: 0; /* 移除负边距 */
}
#chart-container {
    height: 50vh; /* 自适应高度 */
    width: 100%;
}
/* 确保供货统计行可见 */
#supplier_total_withdraw, #supplier_today_commission, #supplier_pending_withdraw {
    display: inline !important;
    visibility: visible !important;
}
.col-xs-4.col-md-4.push-inner-top-bottom {
    display: block !important;
    visibility: visible !important;
}
</style>

<!--modal-->
<div class="modal fade" align="left" id="Nginx-safe" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title" id="myModalLabel">Nginx服务器静态资源目录assets文件上传漏洞修复方法</h4>
            </div>
            <div class="modal-body">
                <H4 style="color:red"># 宝塔-Nginx版本</H4>
                <pre><code>
#宝塔安全规则 By 神秘人
#禁止在静态目录运行动态文件
location ~ ^/assets/(.*)\.(php|php3|php5|asp) {
    return 403;
}
#禁止下载压缩包
location ~ (.*)\.(zip|rar|7z|tar|gz) {
    return 403;
}
#宝塔静态资源目录安全规则 end
                </code></pre>
                将以上代码，复制到宝塔面板->网站->您的网站->设置->配置文件下方类似的代码之前<br>
                其中的php-70.conf会根据php版本显示，例如版本是5.6的会显示php-56.conf;
                <pre><code>
#PHP-INFO-START  PHP引用配置，可以注释或修改
include enable-php-70.conf;
#PHP-INFO-END
                </code></pre>
                <H4 style="color:red"># 以下是PHP版本为7.0版本的修改结果示例</H4>
                <pre><code>
#ERROR-PAGE-START  错误页配置，可以注释、删除或修改
error_page 404 /404.html;
error_page 502 /502.html;
#ERROR-PAGE-END
location ~ ^/assets/(.*)\.(php|php3|php5|asp)(.*)$ {
    return 403;
}
#PHP-INFO-START  PHP引用配置，可以注释或修改
include enable-php-70.conf;
#PHP-INFO-END
#REWRITE-START URL重写规则引用,修改后将导致面板设置的伪静态规则失效
                </code></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<!--modal end-->

<!--官方公告modal-->
<div class="modal fade" align="left" id="notice" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title">官方公告</h4>
            </div>
            <div class="modal-body notice">
                <div class="notice-title">
                    <h3 id="notice-title"></h3>
                </div>
                <div class="notice-content" id="notice-content"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<!--官方公告modal end-->

<div class="col-md-12 center-block" style="float: none;<?php echo $style ?>">
    <?php hook('admin_index_header'); ?>
    <div class="row">
        <!--左侧部分-->
        <div class="col-sm-12 col-md-8 col-lg-8">
            <div class="row">
                <div class="col-sm-12 col-sm-6 col-lg-6 animated fadeInDown">
                    <a href="javascript:void(0)" class="widget">
                        <div class="widget-content widget-content-mini text-right clearfix">
                            <div class="widget-icon pull-left themed-background">
                                <i class="fa fa-list-ol text-light-op"></i>
                            </div>
                            <div class="text-muted-right">
                                <span class="text-muted-left">订单总数</span>&nbsp;<span id="count1">0</span>&nbsp;条<br>
                                <span class="text-muted-left" style="color: green">今日订单</span>&nbsp;<span id="todayOrders">0</span>&nbsp;条<br>
                                <span class="text-muted-left" style="color: blue">昨日订单</span>&nbsp;<span id="lastOrder">0</span>&nbsp;条
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-sm-12 col-sm-6 col-lg-6 animated fadeInDown">
                    <a href="javascript:void(0)" class="widget">
                        <div class="widget-content widget-content-mini text-right clearfix">
                            <div class="widget-icon pull-left themed-background-warning">
                                <i class="fa fa-hourglass-half text-light-op"></i>
                            </div>
                            <div class="text-muted-right">
                                <span class="text-muted-left" style="color: blue">待处理订单</span>&nbsp;<span id="count3">0</span>&nbsp;条<br>
                                <span class="text-muted-left" style="color: #f9ac15">处理中订单</span>&nbsp;<span id="clzorder">0</span>&nbsp;条<br>
                                <span class="text-muted-left" style="color: red">异常中订单</span>&nbsp;<span id="count15">0</span>&nbsp;条</span>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-sm-12 col-sm-6 col-lg-6 animated fadeInDown">
                    <a href="javascript:void(0)" class="widget">
                        <div class="widget-content widget-content-mini text-right clearfix">
                            <div class="widget-icon pull-left themed-background-danger">
                                <i class="fa fa-rmb text-light-op"></i>
                            </div>
                            <div class="text-muted-right">
                                <span class="text-muted-left" style="color: green">今日交易额</span>&nbsp;<span id="todayMoney">0</span>&nbsp;元<br>
                                <span class="text-muted-left" style="color: #f9ac15">今日利润额</span>&nbsp;<span id="todayProfit">0</span>&nbsp;元<br>
                                <span class="text-muted-left" style="color: blue">今日利润比</span>&nbsp;<span id="todayRatio">0</span>&nbsp;%</span>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-sm-12 col-sm-6 col-lg-6 animated fadeInDown">
                    <a href="javascript:void(0)" class="widget">
                        <div class="widget-content widget-content-mini text-right clearfix">
                            <div class="widget-icon pull-left themed-background-success">
                                <i class="fa fa-rmb text-light-op"></i>
                            </div>
                            <div class="text-muted-right">
                                <span class="text-muted-left" style="color: green;">昨日交易额</span>&nbsp;<span id="lastMoney">0</span>&nbsp;元<br>
                                <span class="text-muted-left" style="color: #f9ac15;">昨日利润额</span>&nbsp;<span id="lastProfit">0</span>&nbsp;元<br>
                                <span class="text-muted-left" style="color: blue;">昨日利润比</span>&nbsp;<span id="lastBili">0</span>&nbsp;%</span>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="row">
                <!--站点数据统计-->
                <div class="col-sm-12 col-md-6 col-lg-6">
                    <div class="widget">
                        <div class="widget-content border-bottom">
                            <span class="pull-right text-muted"><i class="fa fa-circle"></i></span>
                            代理统计
                        </div>
                        <div class="widget-content widget-content-full">
                            <div class="row text-center">
                                <div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
                                    <h4 class="widget-heading"><i class="fa fa-sitemap text-dark push text-cmgg-md"></i><br>
                                    <span class="text-tips text-cmgg-xs">&nbsp;代理总数</span><br><br>
                                    <center><span id="count6"></span>个</font></center></h4>
                                </div>
                                <div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
                                    <h4 class="widget-heading"><i class="fa fa-cloud text-dark push text-cmgg-md"></i><br>
                                    <span class="text-tips text-cmgg-xs">&nbsp;今日新增分站</span><br><br>
                                    <center><span id="count7"></span>个</center></h4>
                                </div>
                                <div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
                                    <h4 class="widget-heading"><i class="fa fa-cloud text-dark push text-cmgg-md"></i><br>
                                    <span class="text-tips text-cmgg-xs">&nbsp;今日新增用户</span><br><br>
                                    <center><span id="daili_user"></span>个</center></h4>
                                </div>
                            </div>
                        </div>
                        <?php if ($conf['data_type'] == 2) { ?>
                        <div class="widget-content widget-content-full">
                            <div class="row text-center">
                                <div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
                                    <h4 class="widget-heading"><i class="fa fa-rmb text-dark push text-cmgg-md"></i><br>
                                    <span class="text-tips text-cmgg-xs">&nbsp;全站累计提成</span><br><br>
                                    <center><span id="daili_point"></span>元</center></h4>
                                </div>
                                <div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
                                    <h4 class="widget-heading"><i class="fa fa-rmb text-dark push text-cmgg-md"></i><br>
                                    <span class="text-tips text-cmgg-xs">&nbsp;今日分站提成</span><br><br>
                                    <center><span id="count8"></span>元</center></h4>
                                </div>
                                <div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
                                    <h4 class="widget-heading"><i class="fa fa-money text-dark push text-cmgg-md"></i><br>
                                    <span class="text-tips text-cmgg-xs">&nbsp;待处理提现</span><br><br>
                                    <center><span id="count11"></span>元</center></h4>
                                </div>
                            </div>
                        </div>
                        <div class="widget-content widget-content-full">
                            <div class="row text-center">
                                <div class="col-xs-4 push-inner-top-bottom border-right">
                                    <h4 class="widget-heading"><i class="fa fa-rmb text-dark push text-cmgg-md"></i><br>
                                    <span class="text-tips text-cmgg-xs">&nbsp;全站代理余额</span><br><br>
                                    <center><span id="daili_money"></span>元</center></h4>
                                </div>
                                <div class="col-xs-4 push-inner-top-bottom border-right">
                                    <h4 class="widget-heading"><i class="fa fa-rmb text-dark push text-cmgg-md"></i><br>
                                    <span class="text-tips text-cmgg-xs">&nbsp;今日分站消费</span><br><br>
                                    <center><span id="fz_xf"></span>元</center></h4>
                                </div>
                                <div class="col-xs-4 push-inner-top-bottom border-right">
                                    <h4 class="widget-heading"><i class="fa fa-money text-dark push text-cmgg-md"></i><br>
                                    <span class="text-tips text-cmgg-xs">&nbsp;今日分站充值</span><br><br>
                                    <center><span id="fz_cz"></span>元</center></h4>
                                </div>
                            </div>
                        </div>
                        <?php } else { ?>
                        <div class="widget-content widget-content-full">
                            <div class="row text-center">
                                <div class="col-xs-6 push-inner-top-bottom border-right border-bottom">
                                    <h4 class="widget-heading"><i class="fa fa-rmb text-dark push text-cmgg-md"></i><br>
                                    <span class="text-tips text-cmgg-xs">&nbsp;今日分站提成</span><br><br>
                                    <center><span id="count8"></span>元</center></h4>
                                </div>
                                <div class="col-xs-6 push-inner-top-bottom border-right border-bottom">
                                    <h4 class="widget-heading"><i class="fa fa-money text-dark push text-cmgg-md"></i><br>
                                    <span class="text-tips text-cmgg-xs">&nbsp;待处理提现</span><br><br>
                                    <center><span id="count11"></span>元</center></h4>
                                </div>
                            </div>
                        </div>
                        <div class="widget-content widget-content-full">
                            <div class="row text-center">
                                <div class="col-xs-6 push-inner-top-bottom border-right">
                                    <h4 class="widget-heading"><i class="fa fa-rmb text-dark push text-cmgg-md"></i><br>
                                    <span class="text-tips text-cmgg-xs">&nbsp;今日分站消费</span><br><br>
                                    <center><span id="fz_xf"></span>元</center></h4>
                                </div>
                                <div class="col-xs-6 push-inner-top-bottom border-right">
                                    <h4 class="widget-heading"><i class="fa fa-money text-dark push text-cmgg-md"></i><br>
                                    <span class="text-tips text-cmgg-xs">&nbsp;今日分站充值</span><br><br>
                                    <center><span id="fz_cz"></span>元</center></h4>
                                </div>
                            </div>
                        </div>
                        <?php } ?>
                    </div>
                </div>
                <!--站点数据统计 end-->
                <!--站点支付流水统计-->
                <div class="col-sm-12 col-md-6 col-lg-6">
                    <div class="widget">
                        <div class="widget-content border-bottom">
                            <span class="pull-right text-muted"><i class="fa fa-circle"></i></span>
                            交易统计
                        </div>
                        <div class="widget-content widget-content-full">
                            <div class="row text-center">
                                <!-- 第一行：供货指标 -->
                                <div class="col-xs-4 col-md-4 push-inner-top-bottom border-bottom">
                                    <h4 class="widget-heading">
                                        <i class="fa fa-money text-dark push-bit text-cmgg-md"></i><br>
                                        <span class="text-tips text-cmgg-xs"> 供货累计提现</span><br><br>
                                        <center><span id="supplier_total_withdraw">0</span>元</center>
                                    </h4>
                                </div>
                                <div class="col-xs-4 col-md-4 push-inner-top-bottom border-left border-bottom">
                                    <h4 class="widget-heading">
                                        <i class="fa fa-money text-dark push-bit text-cmgg-md"></i><br>
                                        <span class="text-tips text-cmgg-xs"> 今日供货提成</span><br><br>
                                        <center><span id="supplier_today_commission">0</span>元</center>
                                    </h4>
                                </div>
                                <div class="col-xs-4 col-md-4 push-inner-top-bottom border-left border-bottom">
                                    <h4 class="widget-heading">
                                        <i class="fa fa-money text-dark push-bit text-cmgg-md"></i><br>
                                        <span class="text-tips text-cmgg-xs"> 供货待处理提现</span><br><br>
                                        <center><span id="supplier_pending_withdraw">0</span>元</center>
                                    </h4>
                                </div>
                                <!-- 第二行：今日支付 -->
                                <div class="col-xs-4 col-md-4 push-inner-top-bottom border-bottom">
                                    <h4 class="widget-heading">
                                        <i class="fa fa-qq text-dark push-bit text-cmgg-md"></i><br>
                                        <span class="text-tips text-cmgg-xs"> 今日QQ钱包</span><br><br>
                                        <center><span id="todayqqpay">0</span>元</center>
                                    </h4>
                                </div>
                                <div class="col-xs-4 col-md-4 push-inner-top-bottom border-left border-bottom">
                                    <h4 class="widget-heading">
                                        <i class="fa fa-wechat text-dark push-bit text-cmgg-md"></i><br>
                                        <span class="text-tips text-cmgg-xs"> 今日微信</span><br><br>
                                        <center><span id="todaywxpay">0</span>元</center>
                                    </h4>
                                </div>
                                <div class="col-xs-4 col-md-4 push-inner-top-bottom border-left border-bottom">
                                    <h4 class="widget-heading">
                                        <i class="fa fa-credit-card text-dark push-bit text-cmgg-md"></i><br>
                                        <span class="text-tips text-cmgg-xs"> 今日支付宝</span><br><br>
                                        <center><span id="todayalipay">0</span>元</center>
                                    </h4>
                                </div>
                                <!-- 第三行：昨日支付 -->
                                <div class="col-xs-4 col-md-4 push-inner-top-bottom">
                                    <h4 class="widget-heading">
                                        <i class="fa fa-qq text-dark push-bit text-cmgg-md"></i><br>
                                        <span class="text-tips text-cmgg-xs"> 昨日QQ钱包</span><br><br>
                                        <center><span id="lastqqpay">0</span>元</center>
                                    </h4>
                                </div>
                                <div class="col-xs-4 col-md-4 push-inner-top-bottom border-left">
                                    <h4 class="widget-heading">
                                        <i class="fa fa-wechat text-dark push-bit text-cmgg-md"></i><br>
                                        <span class="text-tips text-cmgg-xs"> 昨日微信</span><br><br>
                                        <center><span id="lastwxpay">0</span>元</center>
                                    </h4>
                                </div>
                                <div class="col-xs-4 col-md-4 push-inner-top-bottom border-left">
                                    <h4 class="widget-heading">
                                        <i class="fa fa-credit-card text-dark push-bit text-cmgg-md"></i><br>
                                        <span class="text-tips text-cmgg-xs"> 昨日支付宝</span><br><br>
                                        <center><span id="lastalipay">0</span>元</center>
                                    </h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--站点支付流水统计 end-->
            </div>
            <!--图表统计-->
            <div class="row">
                <div class="col-sm-12 col-md-12 col-lg-12">
                    <div class="widget">
                        <div class="widget-content border-bottom">
                            <span id="tjTitle">一周交易金额、订单数量、注册用户、充值金额统计</span>
                            <div style="float: right !important;">
                                <span onclick="getPointData('week')" class="btn btn-success btn-xs">近一周</span>
                                <span onclick="getPointData('month')" class="btn btn-primary btn-xs">近一月</span>
                                <span onclick="getPointData('month3')" class="btn btn-warning btn-xs">近三月</span>
                            </div>
                        </div>
                        <div class="widget-content border-bottom themed-background-muted">
                            <div id="chart-container"></div>
                        </div>
                    </div>
                </div>
            </div>
            <!--图表统计 end-->
        </div>
        <!--左侧部分 end-->

        <!--右侧部分 -->
        <div class="col-sm-12 col-md-4 col-lg-4 animated fadeInRight">
            <div class="widget">
                <div class="widget-content border-bottom">
                    <span class="pull-right text-muted"><i class="fa fa-check"></i></span>
                    安全中心
                </div>
                <ul class="list-group">
                    <?php
                    foreach ($sec_msg as $row) {
                        echo $row;
                    }
                    if (count($sec_msg) == 0) {
                        echo '<li class="list-group-item"><span class="btn-sm btn-success">正常</span>&nbsp;暂未发现网站安全问题</li>';
                    }
                    ?>
                </ul>
            </div>
            <div class="widget">
                <div class="widget-content border-bottom text-dark">
                    <span class="pull-right text-muted"><i class="fa fa-check-square"></i></span>
                    检测更新
                </div>
                <ul class="list-group text-dark" id="checkupdate">
                    <li class="list-group-item">正在获取中</li>
                </ul>
            </div>
            <!-- 程序信息 -->
            <div class="widget">
                <div class="widget-content border-bottom">
                    <span class="pull-right text-muted"><i class="fa fa-circle"></i></span>
                    程序信息
                </div>
                <div class="panel panel-body">
                    <table class="table">
                        <thead>
                            <tr><th>项目</th><th>项目详情</th><th>项目操作</th></tr>
                        </thead>
                        <tbody>
                            <tr><td>授权站节点</td>
                                <td><?php echo getAuthServerName(true); ?></td>
                                <td><a href="javascript:layer.msg('退出后台登录后重新选择即可');">切换</a></td>
                            </tr>
                            <tr><td>程序版本</td><td>V<?php echo $SYSVERSION ?></td><td>-</td></tr>
                            <tr><td>程序版号</td><td>Build <?php echo $conf['version'] ?> 正式版</td><td>-</td></tr>
                            <tr><td>数据库版本</td><td><?php echo $conf['SQLVERSION'] ?></td><td>-</td></tr>
                            <tr><td>PHP版本</td>
                                <td><?php echo phpversion(); if (ini_get('safe_mode')) { echo '线程安全'; } else { echo '非线程安全'; } ?></td>
                                <td>-</td>
                            </tr>
                            <tr><td>Mysql版本</td><td><?php echo $mysqlInfo[0] ?></td><td>-</td></tr>
                            <tr><td>服务器类型</td><td><?php echo PHP_OS . " & " . $_SERVER['SERVER_SOFTWARE'] ?></td><td>-</td></tr>
                            <tr><td>单次最大上传限制</td><td><?php echo ini_get('upload_max_filesize') ?></td><td>-</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- / 程序信息 -->
        </div>
        <!--右侧部分 end-->
    </div>
</div>

<script type="text/javascript" src="<?php echo $cdnpublic ?>echarts/4.4.0-rc.1/echarts.min.js"></script>
<script type="text/javascript" src="<?php echo $cdnpublic ?>toastr.js/latest/toastr.min.js"></script>
<script>
var utf8to16 = function (str) {
    var out, i, len, c;
    var char2, char3;
    out = "";
    len = str.length;
    i = 0;
    while (i < len) {
        c = str.charCodeAt(i++);
        switch (c >> 4) {
            case 0: case 1: case 2: case 3: case 4: case 5: case 6: case 7:
                out += str.charAt(i - 1);
                break;
            case 12: case 13:
                char2 = str.charCodeAt(i++);
                out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F));
                break;
            case 14:
                char2 = str.charCodeAt(i++);
                char3 = str.charCodeAt(i++);
                out += String.fromCharCode(((c & 0x0F) << 12) | ((char2 & 0x3F) << 6) | ((char3 & 0x3F) << 0));
                break;
        }
    }
    return out;
};
$(document).on('click', '.read-notice', function(event) {
    event.preventDefault();
    var content = $(this).data('content');
    var title = $(this).data('title');
    var jump = $(this).data('jump');
    var href = $(this).attr('href');
    if (jump == 1 && 'string' === typeof href && href.indexOf('http') >= 0) {
        window.open(href);
    } else {
        $("#notice-content").html(utf8to16(window.atob(content)));
        $("#notice-title").html(title);
        $("#notice").modal('show');
    }
});
$(document).ready(function(){
    var index = layer.load(0);
    $.ajax({
        type: "GET",
        url: "ajax.php?act=getData&noloadFile=1",
        dataType: 'json',
        async: true,
        success: function(data) {
            layer.close(index);
            $('#title').html('后台管理首页');
            $('#yxts').html(data.yxts);
            $('#count1').html(data.count1);
            $('#count3').html(data.count3);
            $('#count6').html(data.count6);
            $('#count7').html(data.count7);
            $('#count8').html(data.count8);
            $('#count11').html(data.count11);
            $('#count15').html(data.count15);
            $('#clzorder').html(data.clzorder);
            $('#lastOrder').html(data.lastdata.orders);
            $('#lastMoney').html(data.lastdata.money);
            $('#lastProfit').html(data.lastdata.profit);
            $('#lastBili').html(data.lastdata.ratio);
            $('#lastqqpay').html(data.lastdata.qqpay);
            $('#lastwxpay').html(data.lastdata.wxpay);
            $('#lastalipay').html(data.lastdata.alipay);
            $('#todayqqpay').html(data.todaydata.qqpay);
            $('#todaywxpay').html(data.todaydata.wxpay);
            $('#todayalipay').html(data.todaydata.alipay);
            $('#todayOrders').html(data.todaydata.orders);
            $('#todayProfit').html(data.todaydata.profit);
            $('#todayRatio').html(data.todaydata.ratio);
            $('#todayMoney').html(data.todaydata.money);
            $('#fz_xf').html(data.fz_xf);
            $('#fz_cz').html(data.fz_cz);
            $('#daili_user').html(data.daili_user);
            $('#daili_money').html(data.daili_money);
            $('#daili_point').html(data.daili_point);
            $('#supplier_total_withdraw').html(data.supplier_total_withdraw || '0');
            $('#supplier_today_commission').html(data.supplier_today_commission || '0');
            $('#supplier_pending_withdraw').html(data.supplier_pending_withdraw || '0');
            // 调试信息
            console.log('供货数据:', {
                supplier_total_withdraw: data.supplier_total_withdraw,
                supplier_today_commission: data.supplier_today_commission,
                supplier_pending_withdraw: data.supplier_pending_withdraw
            });
            setTimeout(function(){ getPointData('week'); }, 500);
            setTimeout(function(){ getWorkData(); }, 1500);
            setTimeout(function(){ checkupdate(); }, 2500);
        },
        error: function () {
            layer.close(index);
            layer.alert("服务器出现卡顿或异常请求超时，请稍后刷新重试！");
        }
    });
});

function showToastr(msg, type, callback) {
    toastr.options = {
        "tapToDismiss": false,
        "closeButton": true,
        "debug": false,
        "positionClass": "toast-bottom-right",
        "showDuration": "300",
        "hideDuration": "10000",
        "timeOut": "5000",
        "extendedTimeOut": "10000",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut",
        "onclick": callback
    };
    if (type == 'info') toastr.info(msg);
    else if (type == 'warning') toastr.warning(msg);
    else if (type == 'error') toastr.error(msg);
    else toastr.success(msg);
}

function getPointData(type) {
    var $type = (type == "month") ? '2' : (type == "month3") ? '3' : '1';
    var title = (type == "month") ? "一月统计" : (type == "month3") ? "三月统计" : "一周统计";
    $.ajax({
        type: "GET",
        url: "ajax.php?act=getPointData&noloadFile=1&type=" + $type,
        dataType: 'json',
        success: function(data) {
            if (data.code == 0) {
                $("#tjTitle").html(title);
                chartUpdata(data);
            } else {
                layer.alert(data.msg);
            }
        },
        error: function(e) {
            layer.alert('服务器错误，请稍后再试！');
        }
    });
}

var getWorkData = function () {
    $.ajax({
        type: "GET",
        url: "ajax.php?act=getWorkData",
        dataType: 'json',
        success: function(data) {
            if (data.code == 0) {
                if (data.data.new_reply == true) {
                    showToastr('工单系统有新的回复！', 'succ', function(){});
                } else if (data.data.dcl > 0) {
                    showToastr('还有' + data.data.dcl + '个工单未处理，请及时查看', 'error', function(){});
                }
                if (data.data.open === 1) {
                    setTimeout(getWorkData, (data.data.speed <= 0 ? 5 : data.data.speed) * 1000);
                }
                if (data.data.online) {
                    showToastr('<b>在线用户：预计' + data.data.online + '人</b>', 'success', function(){});
                }
                if (data.data.kucunlist && typeof data.data.kucunlist === 'object') {
                    if (data.data.kucunlist.length > 10) {
                        showToastr('提示: 当前有' + data.data.kucunlist.length + '个商品的库存小于等于' + data.data.notify_stock_num + '个, 请处理', 'error', function(){});
                    } else {
                        $.each(data.data.kucunlist, function(index, row) {
                            if (index < 5) {
                                showToastr('提示: 商品<b>' + row.name + '的库存仅剩' + row.stock + '个</b>|' + (row.is_curl == 4 ? '点击加卡' : '点击改库存'), 'error', function() {
                                    window.location.href = row.is_curl == 4 ? './fakakms.php?my=add&tid=' + row.tid : './shopedit.php?my=edit&tid=' + row.tid;
                                });
                            }
                        });
                    }
                }
            } else {
                layer.alert(data.msg);
            }
        },
        error: function(e) {
            layer.alert('服务器错误，请稍后再试！');
        }
    });
};

var checkupdate = function () {
    $.ajax({
        type: "GET",
        url: "ajax.php?act=checkupdate",
        dataType: 'json',
        success: function(data) {
            if (data.code == 0) {
                $("#checkupdate").html('');
                $("#checkupdate").append(data.data.msg);
                if (data.data.notice) {
                    $("#checkupdate").append(data.data.notice);
                }
            }
        },
        error: function(e) {
            layer.alert('服务器错误，请稍后再试！');
        }
    });
};

var chartUpdata = function(data) {
    var chartData = data.chart || data.data;
    var myChart = echarts.init(document.getElementById('chart-container'));
    var option = {
        title: { text: '<?php echo $is_mb === true ? '' : '运营统计数据' ?>' },
        tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'cross', label: { backgroundColor: '#6a7985' } }
        },
        legend: { data: ['交易金额', '订单数量', '注册用户', '充值金额'] },
        xAxis: { data: chartData.date },
        grid: { left: '1%', right: '1%', bottom: '1%', containLabel: true },
        yAxis: {},
        series: [
            { name: '交易金额', type: 'line', areaStyle: {}, label: { normal: { show: true, position: 'top' } }, data: chartData.money },
            { name: '订单数量', type: 'line', areaStyle: {}, label: { normal: { show: true, position: 'top' } }, data: chartData.orders },
            { name: '注册用户', type: 'line', areaStyle: {}, data: chartData.users },
            { name: '充值金额', type: 'line', areaStyle: {}, label: { normal: { show: true, position: 'bottom' } }, data: chartData.recharge }
        ]
    };
    myChart.setOption(option);
};
</script>
<script type="text/javascript" src="<?php echo $cdnserver ?>assets/public/Swiper/4.5.0/js/swiper.min.js?<?php echo $jsver ?>"></script>
<script type="text/javascript">
new Swiper('.article-content', {
    direction: 'vertical',
    loop: true,
    autoplay: { delay: 6000, stopOnLastSlide: false, disableOnInteraction: false }
});
</script>
<?php hook('admin_index_footer'); ?>
<?php include 'footer.php'; ?>